<template>
    <div class="email-content"  v-if="!showTrackDetail">
        <!-- 邮件详情加载动画 -->
        <div v-if="detailLoading" class="email-loading-overlay">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">加载中...</div>
          </div>
        </div>
        <div v-if="filteredEmails.length > 0 && selectedEmailDetail && selectedEmailDetail">
          <!-- 顶部导航栏 -->

          <!-- <div class="email-header">
            <div class="email-toolbar">
              <div class="toolbar-group">
                <button class="action-button" @click="replyEmail">
                  回复
                </button>
                <button class="action-button" @click="replyAllEmail">
                  回复全部
                </button>
                <button class="action-button" @click="forwardEmail">
                  转发
                </button>
                <button class="action-button" @click="toggleStarAndTop">
                  {{ selectedEmailDetail.isStarred ? '取消星标' : '星标' }}
                </button>
                <button class="action-button" @click="toggleLanguageSelector">
                  翻译
                </button>
              </div>
            </div>
            <div class="email-title-container">
              <h2 class="email-title">{{ selectedEmailDetail.subject }}</h2>
              <div class="email-tags-inline">
                <span
                  v-for="(tagId, tagIndex) in selectedEmailDetail.tags || []"
                  :key="tagIndex"
                  class="email-tag-label"
                  :style="{ backgroundColor: getTagColor(tagId) }"
                >
                  {{ getTagName(tagId) }}
                  <x-icon class="icon-tiny" @click="removeTagFromEmail(selectedEmailDetail, tagId)" />
                </span>
                <span
                  v-if="selectedEmailDetail.archiveFolder"
                  class="email-archive-folder-label"
                  :style="{ backgroundColor: getArchiveFolderColor(selectedEmailDetail.archiveFolder) }"
                >
                  <folder-icon class="icon-tiny" />
                  {{ getArchiveFolderName(selectedEmailDetail.archiveFolder) }}
                  <x-icon class="icon-tiny" @click="removeFromArchive(selectedEmailDetail)" />
                </span>
              </div>
            </div>
          </div> -->

          <div class="email-meta-container">
          <div class="email-meta">
            <div class="sender-info">
              <div class="detail-label">发件人：</div>
              <div class="recipients">
                <el-tooltip content="点击复制" placement="bottom" effect="light">
                  <span
                    class="recipient copy-text"
                    @click="copyToClipboard(selectedEmailDetail.sendEmailAddress)"
                    style="cursor: pointer;"
                  >
                    {{ selectedEmailDetail.sendEmailAddress }}
                  </span>
                </el-tooltip>
                <span v-if="selectedEmailDetail.tag">@{{ selectedEmailDetail.tag }}</span>
              </div>
            </div>
            <div class="email-date">{{ selectedEmailDetail.sentTime }}</div>
          </div>

          <div class="email-meta">
            <div class="sender-info">
              <div class="detail-label">收件人：</div>
              <div class="recipients">
                <el-tooltip
                  v-for="(recipient, idx) in (showAllTo ? selectedEmailDetail.toList : selectedEmailDetail.toList.slice(0, 50))"
                  :key="idx"
                  content="点击复制"
                  placement="bottom"
                  effect="light"
                >
                  <span
                    class="recipient copy-text"
                    @click="copyToClipboard(recipient.emailAddress)"
                    style="cursor: pointer;"
                  >
                    {{ recipient.emailAddress }}
                  </span>
                </el-tooltip>
                <span
                  v-if="selectedEmailDetail.toList && selectedEmailDetail.toList.length > 50"
                  class="more-recipients"
                  @click="toggleToExpand"
                >
                  {{ showAllTo ? '收起' : `等${selectedEmailDetail.toList.length - 50}人` }}
                </span>
              </div>
            </div>
          </div>

          <!-- 抄送人信息 -->
          <div class="email-meta" v-if="selectedEmailDetail.ccList && selectedEmailDetail.ccList.length > 0">
            <div class="sender-info">
              <div class="detail-label">抄送人：</div>
              <div class="recipients">
                <span v-for="(recipient, idx) in selectedEmailDetail.ccList" :key="idx" class="recipient">
                  {{ recipient.emailAddress }}
                </span>
                <span v-if="selectedEmailDetail.ccList.length > 3" class="more-recipients" @click="toggleCcExpand">
                  {{ showAllCc ? '收起' : `等${selectedEmailDetail.ccList.length - 3}人` }}
                </span>
              </div>
            </div>
          </div>

           <!-- 密送人信息 -->
          <div class="email-meta" v-if="selectedEmailDetail.bccList && selectedEmailDetail.bccList.length > 0">
            <div class="sender-info">
              <div class="detail-label">密送人：</div>
              <div class="recipients">
                <span v-for="(recipient, idx) in selectedEmailDetail.bccList" :key="idx" class="recipient">
                  {{ recipient.emailAddress }}
                </span>
                <span v-if="selectedEmailDetail.bccList.length > 3" class="more-recipients" @click="toggleCcExpand">
                  {{ showAllCc ? '收起' : `等${selectedEmailDetail.bccList.length - 3}人` }}
                </span>
              </div>
            </div>
          </div>

          <!-- 邮件详情信息 -->
          <div class="email-meta email-details-section">
            <div class="email-details-container">
              <div class="detail-group">
                <div class="detail-item">
                  <div class="detail-label">发送时间：</div>
                  <div class="detail-value">{{ formatDateTime(selectedEmailDetail.sentTime) }}</div>
                </div>
                <div class="detail-item" v-if="selectedEmailDetail.receivedTime">
                  <div class="detail-label">接收时间：</div>
                  <div class="detail-value">{{ formatDateTime(selectedEmailDetail.receivedTime) || '-'}}</div>
                </div>
                <div class="detail-item" v-else>
                  <div class="detail-label"></div>
                  <div class="detail-value"></div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">优先级：</div>
                  <div class="detail-value">{{ selectedEmailDetail.priority == 1 ? '普通' : '一对一' }}</div>
                </div>
              </div>
              <div class="detail-group">
                <div class="detail-item">
                  <div class="detail-label">目录：</div>
                  <div class="detail-value">{{ getActiveFolder(selectedEmailDetail.status) }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">归属账号：</div>
                  <div class="detail-value">{{ selectedEmailDetail.belongMailAccountAddress }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">拥有人：</div>
                  <div class="detail-value">{{ selectedEmailDetail.belongMailAccountUserName }}</div>
                </div>
              </div>
            </div>
          </div>

          </div>

          <!-- AI智能摘要按钮区域 -->
          <!-- <div class="ai-summary-section" v-if="!showAiSummary">
            <button
              class="ai-summary-btn"
              @click="generateAiSummary"
              :disabled="isGeneratingSummary"
            >
              <zap-icon class="icon-small" />
              {{ isGeneratingSummary ? '生成中...' : '生成 AI 智能摘要' }}
            </button>
          </div> -->

          <!-- AI智能摘要结果显示 -->
          <div class="ai-summary-result" v-if="showAiSummary">
            <div class="summary-header">
              <div class="summary-title">
                <zap-icon class="icon-small" />
                邮件智能摘要
              </div>
              <button class="close-summary-btn" @click="closeAiSummary">
                <x-icon class="icon-tiny" />
              </button>
            </div>
            <div class="summary-content">
              <div v-if="isGeneratingSummary" class="summary-loading">
                <rotate-cw-icon class="icon-small spin" /> 正在生成智能摘要...
              </div>
              <div v-else class="summary-text">
                {{ aiSummaryContent }}
              </div>
            </div>
          </div>

          <!-- 语言选择面板 -->
          <div class="language-selector" v-if="showLanguageSelector">
            <div class="language-selector-inline">
              <div class="language-label">
                <Languages-icon class="icon-small" />
              </div>
              <div class="language-selects">
                <select v-model="translationSourceLanguage" class="source-lang">
                  <option value="auto">自动检测</option>
                  <option v-for="lang in WKConfig.languages" :key="lang.code" :value="lang.code">{{ lang.name }}</option>
                </select>
                <div class="arrow-icon">→</div>
                <select v-model="translationLanguage" class="target-lang">
                  <option v-for="lang in WKConfig.languages" :key="lang.code" :value="lang.code">{{ lang.name }}</option>
                </select>
              </div>
              <div class="translation-actions-inline">
                <button class="translate-btn" @click="performTranslation">
                  <Languages-icon class="icon-small" /> 翻译
                </button>
                <button class="close-selector-btn" @click="showLanguageSelector = false">
                  <x-icon class="icon-tiny" /> 关闭翻译
                </button>
              </div>
            </div>
          </div>
          <!-- 附件显示区域 -->
          <div class="email-attachments" v-if="selectedEmailDetail.fileList && selectedEmailDetail.fileList.length > 0">
            <div class="attachments-header">
              <paperclip-icon class="icon-small" /> 附件 ({{ selectedEmailDetail.fileList.length }})
            </div>
            <div class="attachments-list">
              <div
                v-for="(attachment, index) in selectedEmailDetail.fileList"
                :key="index"
                class="attachment-item"
              >
                <div class="attachment-icon">
                  <file-text-icon v-if="isDocumentFile(attachment.name)" class="icon" />
                  <image-icon v-else-if="isImageFile(attachment.name)" class="icon" />
                  <file-icon v-else class="icon" />
                </div>
                <div class="attachment-info">
                  <div class="attachment-name">{{ attachment.name }}</div>
                  <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
                </div>
                <div class="attachment-actions">
                  <eye-icon
                    v-if="canPreviewFile(attachment.name)"
                    class="icon-small"
                    title="预览"
                    @click.stop="previewAttachment(attachment)"
                  />
                  <download-icon class="icon-small" title="下载" @click.stop="downloadAttachment(attachment)" />
                </div>
              </div>
            </div>
          </div>

          <!-- 邮件内容区域 -->
          <div class="email-content-wrapper">
            <!-- 原始邮件内容 -->
            <div v-show="!showTranslation" class="original-content">
              <div class="email-body" v-html="selectedEmailDetail && selectedEmailDetail.content"></div>
              <div class="email-signature" v-if="selectedEmailDetail.signature">
                <div class="signature-content" v-html="selectedEmailDetail.signature"></div>
              </div>
            </div>
            <!-- 翻译结果 -->
            <div class="translation-result" v-if="showTranslation">
              <div class="translation-header">
                <div>翻译结果 ({{ getLanguageName(translationLanguage) }})</div>
                <div class="translation-actions">
                  <button class="view-original" @click="toggleOriginalView">
                    {{ showOriginalContent ? '隐藏原文' : '显示原文' }}
                  </button>
                  <button class="close-translation" @click="closeTranslation">
                    <x-icon class="icon-tiny" /> 关闭
                  </button>
                </div>
              </div>
              <div class="translation-content">
                <div v-if="isTranslating" class="translation-loading">
                  <rotate-cw-icon class="icon-small spin" /> 正在翻译...
                </div>
                <div v-else class="translated-text" v-html="translatedContent"></div>
              </div>

              <!-- 原文内容（可切换显示） -->
              <div v-if="showOriginalContent" class="original-content-preview">
                <div class="original-content-header">原文内容</div>
                <div class="email-body" v-html="selectedEmailDetail && selectedEmailDetail.content"></div>
                <div class="email-signature" v-if="selectedEmailDetail.signature">
                  <div class="signature-header">签名</div>
                  <div class="signature-content" v-html="selectedEmailDetail.signature"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-data-container">
          <img
            class="no-data"
            src="@/assets/img/noMail.png">
          <div class="no-data-name">没有查到邮件</div>
      </div>
      </div>
</template>
<script>
import { mapState } from 'vuex'
import {
  X, Folder, Zap, RotateCw, Languages, Paperclip,
  FileText, Image, File, Eye, Download
} from 'lucide-vue'
export default {
  name: 'EmailDetails',
  components: {
    XIcon: X,
    FolderIcon: Folder,
    ZapIcon: Zap,
    RotateCwIcon: RotateCw,
    LanguagesIcon: Languages,
    PaperclipIcon: Paperclip,
    FileTextIcon: FileText,
    ImageIcon: Image,
    FileIcon: File,
    EyeIcon: Eye,
    DownloadIcon: Download
  },
  props: {
    selectedEmailDetail: {
      type: Object,
      default: () => ({})
    },
    filteredEmails: {
      type: Array,
      default: () => []
    },
    showTrackDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 加载状态
      detailLoading: false,

      // 抄送人展开/收起状态
      showAllCc: false,
      // 收件人展开/收起状态
      showAllTo: false,

      // 翻译相关
      showTranslation: false,
      translationLanguage: 'zh', // 默认翻译为中文
      translationSourceLanguage: 'auto', // 默认自动检测源语言
      translatedContent: '',
      isTranslating: false,
      showLanguageSelector: false,
      showOriginalContent: false, // 是否在翻译结果中显示原文

      // AI智能摘要相关
      showAiSummary: false,
      isGeneratingSummary: false,
      aiSummaryContent: '',

      // 标签相关数据
      tags: [
        // 系统标签 (ID 1-9)
        { id: 1, name: '通知', color: '#e60012' },
        { id: 2, name: '招聘', color: '#e60012' },
        { id: 3, name: '商机', color: '#e60012' },
        { id: 4, name: '报价', color: '#e60012' },
        { id: 5, name: '已更回复', color: '#e60012' },
        { id: 6, name: 'PI', color: '#e60012' },
        { id: 7, name: '订单', color: '#e60012' },
        { id: 8, name: '样品', color: '#e60012' },
        { id: 9, name: '询盘', color: '#e60012' },
        // 自定义标签 (ID > 9)
        { id: 10, name: '客户投诉', color: '#4caf50' },
        { id: 11, name: '业务跟进', color: '#9c27b0' },
        { id: 12, name: '客户不感兴趣', color: '#795548' },
        { id: 13, name: '重点客户', color: '#00bcd4' },
        { id: 14, name: '三次未回复', color: '#ff9800' }
      ],

      // 归档文件夹相关数据
      archiveFolders: [
        { id: 1, name: '项目文件', color: '#4caf50' },
        { id: 2, name: '供应商', color: '#2196f3' },
        { id: 3, name: '客户询盘', color: '#ff9800' }
      ],

      // 附件预览相关
      showPreviewModal: false,
      previewingAttachment: null,
      previewContent: '',
      previewUrl: '',

      // 提示消息
      showToast: false,
      toastMessage: '',
      toastType: 'success', // 'success' 或 'error'
      toastTimeout: null,
    }
  },
  watch: {
    // 监听邮件详情变化，处理图片自适应
    selectedEmailDetail: {
      handler(newVal) {
        if (newVal && newVal.content) {
          this.handleEmailContentImages()
        }
      },
      immediate: true,
      deep: true
    },

    // 监听翻译内容变化
    translatedContent: {
      handler(newVal) {
        if (newVal) {
          this.handleEmailContentImages()
        }
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),

    // 是否有上一封邮件
    hasPreviousEmail() {
      if (!this.filteredEmails || !this.selectedEmailDetail) return false
      const currentIndex = this.filteredEmails.findIndex(email => email.id === this.selectedEmailDetail.id)
      return currentIndex > 0
    },

    // 是否有下一封邮件
    hasNextEmail() {
      if (!this.filteredEmails || !this.selectedEmailDetail) return false
      const currentIndex = this.filteredEmails.findIndex(email => email.id === this.selectedEmailDetail.id)
      return currentIndex >= 0 && currentIndex < this.filteredEmails.length - 1
    }
  },
  mounted() {
    // 初始化图片处理
    this.handleEmailContentImages()

    // 监听窗口大小变化，重新调整图片
    window.addEventListener('resize', this.handleEmailContentImages)
  },
  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('resize', this.handleEmailContentImages)
  },
  methods: {
    // 复制到剪贴板
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.showToastMessage('已复制到剪贴板', 'success')
        }).catch(() => {
          this.fallbackCopyToClipboard(text)
        })
      } else {
        this.fallbackCopyToClipboard(text)
      }
    },

    // 备用复制方法
    fallbackCopyToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        this.showToastMessage('已复制到剪贴板', 'success')
      } catch (err) {
        this.showToastMessage('复制失败', 'error')
      }
      document.body.removeChild(textArea)
    },

    // 显示提示消息
    showToastMessage(message, type = 'success') {
      this.toastMessage = message
      this.toastType = type
      this.showToast = true

      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout)
      }

      this.toastTimeout = setTimeout(() => {
        this.showToast = false
      }, 3000)
    },

    // 切换收件人展开状态
    toggleToExpand() {
      this.showAllTo = !this.showAllTo
    },

    // 切换抄送人展开状态
    toggleCcExpand() {
      this.showAllCc = !this.showAllCc
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 获取标签颜色
    getTagColor(tagId) {
      const tag = this.tags.find(t => t.id === tagId)
      return tag ? tag.color : '#e60012'
    },

    // 获取标签名称
    getTagName(tagId) {
      const tag = this.tags.find(t => t.id === tagId)
      return tag ? tag.name : '未知标签'
    },

    // 获取归档文件夹颜色
    getArchiveFolderColor(folderId) {
      const folder = this.archiveFolders.find(f => f.id === folderId)
      return folder ? folder.color : '#4caf50'
    },

    // 获取归档文件夹名称
    getArchiveFolderName(folderId) {
      const folder = this.archiveFolders.find(f => f.id === folderId)
      return folder ? folder.name : '未知文件夹'
    },

    // 获取当前文件夹名称
    getActiveFolder(status) {
      const folderMap = {
        'inbox': '收件箱',
        'sent': '已发送',
        'draft': '草稿箱',
        'sent_trash': '已发送垃圾箱',
        'spam': '垃圾邮件',
        'pending': '待发送'
      }
      return folderMap[status] || '收件箱'
    },

    // 从邮件中移除标签
    removeTagFromEmail(email, tagId) {
      this.$emit('remove-tag', email, tagId)
    },

    // 从归档中移除
    removeFromArchive(email) {
      this.$emit('remove-from-archive', email)
    },

    // 邮件工具栏事件处理
    replyEmail() {
      this.$emit('reply', this.selectedEmailDetail)
    },

    replyAllEmail() {
      this.$emit('reply-all', this.selectedEmailDetail)
    },

    forwardEmail() {
      this.$emit('forward', this.selectedEmailDetail)
    },

    showArchiveOptions() {
      this.$emit('archive', this.selectedEmailDetail)
    },

    openTagModal() {
      this.$emit('tag', this.selectedEmailDetail)
    },

    openDistributeModal() {
      this.$emit('distribute', this.selectedEmailDetail)
    },

    toggleLanguageSelector() {
      this.showLanguageSelector = !this.showLanguageSelector
    },

    toggleStarAndTop() {
      this.$emit('star', this.selectedEmailDetail)
    },

    handleDeleteEmail() {
      this.$emit('delete', this.selectedEmailDetail)
    },

    handleSetReminder() {
      this.$emit('set-reminder', this.selectedEmailDetail)
    },

    navigateEmail(direction) {
      this.$emit('navigate', direction)
    },

    openFullscreenView() {
      this.$emit('fullscreen', this.selectedEmailDetail)
    },

    addNewClue() {
      this.$emit('add-newclues', this.selectedEmailDetail)
    },

    addSalesOrder() {
      this.$emit('add-salesorder', this.selectedEmailDetail)
    },

    viewInNewTab() {
      this.$emit('view-new-tab', this.selectedEmailDetail)
    },

    handleEditDraft() {
      this.$emit('edit-draft', this.selectedEmailDetail)
    },

    // AI智能摘要相关方法
    generateAiSummary() {
      this.isGeneratingSummary = true
      // 模拟AI摘要生成
      setTimeout(() => {
        this.aiSummaryContent = '这是一封关于产品询价的邮件，客户询问了产品规格、价格和交货期等信息。'
        this.showAiSummary = true
        this.isGeneratingSummary = false
      }, 2000)
    },

    closeAiSummary() {
      this.showAiSummary = false
      this.aiSummaryContent = ''
    },

    // 翻译相关方法
    performTranslation() {
      this.isTranslating = true
      this.showTranslation = true

      // 模拟翻译过程
      setTimeout(() => {
        this.translatedContent = '这是翻译后的内容...'
        this.isTranslating = false
        this.showLanguageSelector = false
      }, 2000)
    },

    closeTranslation() {
      this.showTranslation = false
      this.translatedContent = ''
      this.showOriginalContent = false
    },

    toggleOriginalView() {
      this.showOriginalContent = !this.showOriginalContent
    },

    getLanguageName(code) {
      const lang = WKConfig.languages.find(l => l.code === code)
      return lang ? lang.name : code
    },

    // 处理邮件内容中的图片自适应
    handleEmailContentImages() {
      this.$nextTick(() => {
        // 处理邮件正文中的图片
        const emailBodies = this.$el.querySelectorAll('.email-body')
        emailBodies.forEach(emailBody => {
          this.processImagesInContainer(emailBody)
        })

        // 处理翻译内容中的图片
        const translatedText = this.$el.querySelector('.translated-text')
        if (translatedText) {
          this.processImagesInContainer(translatedText)
        }

        // 处理所有签名内容中的图片
        const signatureContents = this.$el.querySelectorAll('.signature-content')
        signatureContents.forEach(signatureContent => {
          this.processImagesInContainer(signatureContent)
        })
      })
    },

    // 处理容器中的图片
    processImagesInContainer(container) {
      const images = container.querySelectorAll('img')
      const isSignatureContainer = container.classList.contains('signature-content')

      images.forEach(img => {
        // 为图片添加标识类
        if (isSignatureContainer) {
          img.classList.add('signature-image')
        } else {
          img.classList.add('email-content-image')
        }

        // 根据容器类型应用不同的样式处理
        if (isSignatureContainer) {
          this.processSignatureImage(img)
        } else {
          this.processContentImage(img)
        }

        // 添加点击放大功能
        if (!img.hasAttribute('data-preview-added')) {
          img.addEventListener('click', (e) => {
            this.showImagePreview(e.target.src)
          })
          img.setAttribute('data-preview-added', 'true')
        }
      })

      // 处理其他媒体元素
      const mediaElements = container.querySelectorAll('video, audio, iframe, embed, object')
      mediaElements.forEach(element => {
        element.style.maxWidth = '100%'
        element.style.height = 'auto'
      })
    },

    // 处理签名图片
    processSignatureImage(img) {
      // 移除可能影响显示的内联样式
      img.style.removeProperty('width')
      img.style.removeProperty('height')

      // 设置签名图片的合理限制
      img.style.maxWidth = '200px' // 限制最大宽度为200px
      img.style.maxHeight = '100px' // 限制最大高度为100px
      img.style.height = 'auto'
      img.style.width = 'auto'
      img.style.objectFit = 'contain'

      // 如果图片加载完成，检查其原始尺寸
      if (img.complete) {
        this.adjustSignatureImageSize(img)
      } else {
        img.onload = () => {
          this.adjustSignatureImageSize(img)
        }
      }
    },

    // 处理邮件正文图片
    processContentImage(img) {
      // 移除可能的内联样式中的固定宽度和高度
      img.style.maxWidth = '100%'
      img.style.height = 'auto'
      img.style.width = 'auto'

      // 添加加载完成后的处理
      if (img.complete) {
        this.adjustContentImageSize(img)
      } else {
        img.onload = () => {
          this.adjustContentImageSize(img)
        }
      }
    },

    // 调整签名图片尺寸
    adjustSignatureImageSize(img) {
      const naturalWidth = img.naturalWidth
      const naturalHeight = img.naturalHeight

      // 如果是小图片（通常是logo或图标），保持原始尺寸
      if (naturalWidth <= 200 && naturalHeight <= 100) {
        img.style.width = naturalWidth + 'px'
        img.style.height = naturalHeight + 'px'
        img.style.maxWidth = 'none'
        img.style.maxHeight = 'none'
      }
      // 如果图片较大，则应用限制
      else {
        img.style.maxWidth = '200px'
        img.style.maxHeight = '100px'
        img.style.width = 'auto'
        img.style.height = 'auto'
      }
    },

    // 调整邮件正文图片尺寸
    adjustContentImageSize(img) {
      const containerWidth = img.parentElement.offsetWidth
      if (img.naturalWidth > containerWidth) {
        img.style.width = '100%'
        img.style.height = 'auto'
      }
    },

    // 显示图片预览
    showImagePreview(src) {
      // 创建预览模态框
      const modal = document.createElement('div')
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        cursor: pointer;
      `

      const img = document.createElement('img')
      img.src = src
      img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      `

      modal.appendChild(img)
      document.body.appendChild(modal)

      // 点击关闭预览
      modal.addEventListener('click', () => {
        document.body.removeChild(modal)
      })

      // ESC键关闭预览
      const handleKeydown = (e) => {
        if (e.key === 'Escape') {
          document.body.removeChild(modal)
          document.removeEventListener('keydown', handleKeydown)
        }
      }
      document.addEventListener('keydown', handleKeydown)
    },

    // 附件相关方法
    isDocumentFile(filename) {
      const docExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']
      return docExtensions.some(ext => filename.toLowerCase().endsWith(ext))
    },

    isImageFile(filename) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
      return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext))
    },

    canPreviewFile(filename) {
      return this.isImageFile(filename) || filename.toLowerCase().endsWith('.pdf')
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    previewAttachment(attachment) {
      this.previewingAttachment = attachment
      this.showPreviewModal = true
      // 这里可以添加预览逻辑
    },

    downloadAttachment(attachment) {
      // 这里可以添加下载逻辑
      this.showToastMessage('开始下载附件', 'success')
    }
  }
}
</script>
<style scoped>
/* 邮件详情容器 */
.email-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-left: 1px solid #e8e8e8;
  position: relative;
  height: 100vh;
  min-height: 0;
  overflow: auto;
}

/* 邮件加载动画 */
.email-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 14px;
}

/* 邮件头部 */
.email-header {
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
  padding: 0;
}

/* 邮件工具栏样式 */
.email-toolbar {
  padding: 12px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fff;
}

.toolbar-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.action-button {
  padding: 6px 12px;
  background-color: #f5f7fa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-button:hover {
  background-color: #e6e9ed;
  border-color: #1890ff;
}

.email-title-container {
  padding: 16px 20px;
}

.email-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  line-height: 1.4;
  word-break: break-word;
}

/* 邮件标签和归档文件夹 */
.email-tags-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.email-tag-label, .email-archive-folder-label {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
  font-weight: 500;
  gap: 4px;
}

.email-tag-label .icon-tiny,
.email-archive-folder-label .icon-tiny {
  width: 12px;
  height: 12px;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.email-tag-label .icon-tiny:hover,
.email-archive-folder-label .icon-tiny:hover {
  opacity: 1;
}

/* 邮件元信息容器 */
.email-meta-container {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fff;
}

.email-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  font-size: 14px;
}

.email-meta:last-child {
  margin-bottom: 0;
}

.sender-info {
  display: flex;
  align-items: flex-start;
  flex: 1;
  gap: 8px;
}

.detail-label {
  color: #909399;
  font-weight: 500;
  min-width: 60px;
  flex-shrink: 0;
}

.recipients {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  flex: 1;
}

.recipient {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.2s;
}

.recipient:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.copy-text {
  cursor: pointer;
}

.more-recipients {
  color: #1890ff;
  cursor: pointer;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: #f0f9ff;
  transition: background-color 0.2s;
}

.more-recipients:hover {
  background-color: #e6f7ff;
}

.email-date {
  color: #909399;
  font-size: 13px;
  white-space: nowrap;
  margin-left: 16px;
}

/* 邮件详情信息区域 */
.email-details-section {
  margin-top: 8px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
}

.email-details-container {
  display: flex;
  gap: 40px;
}

.detail-group {
  flex: 1;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .detail-label {
  color: #909399;
  min-width: 80px;
  font-weight: 500;
}

.detail-item .detail-value {
  color: #606266;
  flex: 1;
}

/* AI智能摘要区域 */
.ai-summary-section {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #f8f9fa;
  text-align: center;
}

.ai-summary-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.ai-summary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.ai-summary-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.ai-summary-result {
  margin: 16px 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.summary-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
  font-size: 15px;
}

.close-summary-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #909399;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-summary-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #606266;
}

.summary-content {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

.summary-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
}

.summary-text {
  white-space: pre-wrap;
}

/* 语言选择面板 */
.language-selector {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #f0f9ff;
}

.language-selector-inline {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.language-label {
  display: flex;
  align-items: center;
  color: #1890ff;
  font-weight: 500;
}

.language-selects {
  display: flex;
  align-items: center;
  gap: 12px;
}

.source-lang, .target-lang {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  min-width: 120px;
}

.arrow-icon {
  color: #1890ff;
  font-weight: bold;
  font-size: 16px;
}

.translation-actions-inline {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.translate-btn, .close-selector-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.translate-btn {
  background-color: #1890ff;
  color: white;
}

.translate-btn:hover {
  background-color: #40a9ff;
}

.close-selector-btn {
  background-color: #f5f5f5;
  color: #666;
}

.close-selector-btn:hover {
  background-color: #e8e8e8;
}

/* 附件显示区域 */
.email-attachments {
  margin: 16px 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.attachments-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #303133;
  font-size: 15px;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  transition: all 0.2s;
}

.attachment-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.attachment-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #f0f9ff;
  border-radius: 4px;
  color: #1890ff;
}

.attachment-info {
  flex: 1;
  min-width: 0;
}

.attachment-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  word-break: break-all;
  margin-bottom: 2px;
}

.attachment-size {
  color: #909399;
  font-size: 12px;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.attachment-actions .icon-small {
  width: 18px;
  height: 18px;
  color: #1890ff;
  cursor: pointer;
  transition: color 0.2s;
}

.attachment-actions .icon-small:hover {
  color: #40a9ff;
}

/* 邮件内容区域 */
.email-content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 20px 20px 40px 20px; /* 增加底部padding，防止内容被遮挡 */
  min-height: 0;
  box-sizing: border-box;
  scroll-behavior: smooth; /* 平滑滚动 */
}

/* 自定义滚动条样式 */
.email-content-wrapper::-webkit-scrollbar {
  width: 6px;
}

.email-content-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.email-content-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.2s;
}

.email-content-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.original-content, .translation-result {
  line-height: 1.6;
}

.original-content {
  padding-bottom: 20px; /* 增加底部padding */
  margin-bottom: 20px; /* 增加底部margin */
}

.email-body {
  color: #303133;
  font-size: 14px;
  word-wrap: break-word;
  margin-bottom: 30px; /* 增加底部间距 */
  overflow-wrap: break-word;
  word-break: break-word;
}

/* 邮件内容中的图片自适应样式 */
.email-body img {
  display: block;
  margin: 10px 0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  cursor: pointer;
}

/* 邮件正文图片样式 */
.email-content-image {
  max-width: 100% !important;
  height: auto !important;
  width: auto !important;
}

/* 签名图片样式 */
.signature-image {
  max-width: 200px !important;
  max-height: 100px !important;
  height: auto !important;
  width: auto !important;
  object-fit: contain !important;
  display: inline-block !important;
  vertical-align: middle !important;
  margin: 2px 4px !important;
}

/* 确保所有媒体元素都自适应 */
.email-body video,
.email-body audio,
.email-body iframe,
.email-body embed,
.email-body object {
  max-width: 100% !important;
  height: auto !important;
}


/* 处理可能的容器溢出 */
.email-body div,
.email-body p,
.email-body span {
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.email-signature {
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
  padding-bottom: 20px; /* 增加底部padding */
  margin-top: 20px;
  margin-bottom: 20px; /* 增加底部margin */
}

/* 签名内容样式 */
.signature-content {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* 签名内容中的图片容器 */
.signature-content img {
  display: inline-block !important;
  vertical-align: middle !important;
  margin: 2px 4px !important;
  border-radius: 2px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* 签名内容中的链接样式 */
.signature-content a {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.2s;
}

.signature-content a:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 签名内容中的表格样式 */
.signature-content table {
  border-collapse: collapse;
  max-width: 100%;
}

.signature-content td,
.signature-content th {
  padding: 4px 8px;
  border: 1px solid #e8e8e8;
  font-size: 12px;
}

/* 签名内容中的文本样式 */
.signature-content p {
  margin: 4px 0;
  line-height: 1.4;
}

.signature-content div {
  max-width: 100%;
  overflow-wrap: break-word;
}

/* 翻译结果样式 */
.translation-result {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px 16px 24px 16px; /* 增加底部padding */
  margin-bottom: 30px; /* 增加底部margin */
}

.translation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 600;
  color: #303133;
}

.translation-actions {
  display: flex;
  gap: 8px;
}

.view-original, .close-translation {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-original:hover, .close-translation:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.translation-content {
  margin-bottom: 16px;
}

.translation-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-style: italic;
}

.translated-text {
  color: #303133;
  font-size: 14px;
  line-height: 1.6;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* 翻译内容中的图片自适应 */
.translated-text img {
  display: block;
  margin: 10px 0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  cursor: pointer;
}

.translated-text img:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 翻译内容中的签名图片特殊处理 */
.translated-text .signature-image {
  display: inline-block !important;
  margin: 2px 4px !important;
  transform: none !important;
}

.translated-text .signature-image:hover {
  transform: scale(1.05) !important;
}

/* 翻译内容中的其他媒体元素 */
.translated-text video,
.translated-text audio,
.translated-text iframe,
.translated-text embed,
.translated-text object {
  max-width: 100% !important;
  height: auto !important;
}

.translated-text div,
.translated-text p,
.translated-text span {
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.original-content-preview {
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
  padding-bottom: 20px; /* 增加底部padding */
  margin-bottom: 20px; /* 增加底部margin */
}

.original-content-header {
  font-weight: 600;
  color: #606266;
  margin-bottom: 12px;
  font-size: 13px;
}

.signature-header {
  font-weight: 600;
  color: #606266;
  margin-bottom: 8px;
  margin-top: 16px;
  font-size: 13px;
}

/* 无数据状态 */
.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  text-align: center;
  padding: 40px 20px;
}

.no-data {
  width: 120px;
  height: auto;
  margin-bottom: 16px;
  opacity: 0.6;
}

.no-data-name {
  font-size: 16px;
  color: #909399;
}

/* 图标样式 */
.icon-tiny {
  width: 14px;
  height: 14px;
}

.icon-small {
  width: 16px;
  height: 16px;
}

.icon {
  width: 20px;
  height: 20px;
}

/* 动画效果 */
.spin {
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .email-details-container {
    flex-direction: column;
    gap: 16px;
  }

  .language-selector-inline {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .translation-actions-inline {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }

  .detail-item .detail-label {
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .email-title {
    font-size: 18px;
  }

  .email-meta {
    flex-direction: column;
    gap: 8px;
  }

  .email-date {
    margin-left: 0;
  }

  .detail-item .detail-label {
    min-width: 60px;
    font-size: 12px;
  }

  .detail-item .detail-value {
    font-size: 12px;
  }
}
</style>